using Microsoft.EntityFrameworkCore;
using PEMTestSystem.Data;
using PEMTestSystem.Models.Core;
using PEMTestSystem.Models.Devices;
using PEMTestSystem.Models.System;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace PEMTestSystem.Services
{
    /// <summary>
    /// 数据库初始化服务
    /// </summary>
    public class DatabaseInitializer
    {
        private readonly PEMTestDbContext _context;
        private readonly IConfigurationService _configService;

        public DatabaseInitializer(PEMTestDbContext context, IConfigurationService configService)
        {
            _context = context;
            _configService = configService;
        }

        /// <summary>
        /// 初始化数据库
        /// </summary>
        public async Task InitializeAsync()
        {
            try
            {
                App.AlarmService.Info("数据库初始化", "开始数据库初始化...");

                // 检查是否允许自动创建数据库
                if (!_configService.GetBooleanSetting("AutoCreateDatabase", true))
                {
                    App.AlarmService.Info("数据库初始化", "自动创建数据库功能已禁用");
                    return;
                }

                // 确保数据库创建
                var created = await _context.Database.EnsureCreatedAsync();
                if (created)
                {
                    App.AlarmService.Info("数据库初始化", "数据库已创建");
                }

                // 应用挂起的迁移
                if (_configService.GetBooleanSetting("AutoMigrate", true))
                {
                    if ((await _context.Database.GetPendingMigrationsAsync()).Any())
                    {
                        await _context.Database.MigrateAsync();
                        App.AlarmService.Info("数据库初始化", "应用了数据库迁移");
                    }
                }

                // 初始化基础数据
                if (_configService.GetBooleanSetting("SeedDefaultData", true))
                {
                    await SeedDevicesAsync();
                    await SeedSystemConfigurationsAsync();
                    await SeedExperimentTemplatesAsync();
                }

                App.AlarmService.Info("数据库初始化", "数据库初始化完成");
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("数据库初始化", "数据库初始化失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 初始化设备数据
        /// </summary>
        private async Task SeedDevicesAsync()
        {
            if (await _context.Devices.AnyAsync())
                return;

            var devices = new List<Device>
            {
                new Device
                {
                    DeviceType = DeviceType.PowerSupply,
                    DeviceId = "PowerSupply_Main",
                    DeviceName = "主电源",
                    Model = "爱德克斯IT-M3901D",
                    ConnectionType = ConnectionType.Ethernet,
                    ConnectionString = JsonConvert.SerializeObject(new { IP = "*************", Port = 30000 }),
                    Specifications = JsonConvert.SerializeObject(new 
                    { 
                        MaxVoltage = _configService.GetDoubleSetting("MaxVoltage", 10.0), 
                        MaxCurrent = _configService.GetDoubleSetting("MaxCurrent", 170.0), 
                        VoltageAccuracy = 0.001, 
                        CurrentAccuracy = 0.01 
                    })
                },
                new Device
                {
                    DeviceType = DeviceType.Pump1,
                    DeviceId = "Pump_01",
                    DeviceName = "流量泵1",
                    Model = "卡川DI Pump550",
                    ConnectionType = ConnectionType.ModbusRTU,
                    ConnectionString = JsonConvert.SerializeObject(new { Port = "COM3", BaudRate = 9600, Address = 1 }),
                    Specifications = JsonConvert.SerializeObject(new 
                    { 
                        MaxFlowRate = 400.0, 
                        MinFlowRate = 0.1, 
                        Accuracy = 0.01 
                    })
                },
                new Device
                {
                    DeviceType = DeviceType.Pump2,
                    DeviceId = "Pump_02",
                    DeviceName = "流量泵2",
                    Model = "卡川DI Pump550",
                    ConnectionType = ConnectionType.ModbusRTU,
                    ConnectionString = JsonConvert.SerializeObject(new { Port = "COM3", BaudRate = 9600, Address = 2 }),
                    Specifications = JsonConvert.SerializeObject(new 
                    { 
                        MaxFlowRate = 400.0, 
                        MinFlowRate = 0.1, 
                        Accuracy = 0.01 
                    })
                },
                new Device
                {
                    DeviceType = DeviceType.TemperatureController,
                    DeviceId = "TempController_Main",
                    DeviceName = "主温控器",
                    Model = "宇电MK008",
                    ConnectionType = ConnectionType.ModbusRTU,
                    ConnectionString = JsonConvert.SerializeObject(new { Port = "COM4", BaudRate = 9600, Address = 3 }),
                    Specifications = JsonConvert.SerializeObject(new 
                    { 
                        MaxTemperature = _configService.GetDoubleSetting("MaxTemperature", 95.0), 
                        MinTemperature = 20.0, 
                        Accuracy = 0.1 
                    })
                }
            };

            await _context.Devices.AddRangeAsync(devices);
            await _context.SaveChangesAsync();

            App.AlarmService.Info("数据库初始化", $"初始化了 {devices.Count} 个设备");
        }

        /// <summary>
        /// 初始化系统配置数据
        /// </summary>
        private async Task SeedSystemConfigurationsAsync()
        {
            if (await _context.SystemConfigurations.AnyAsync())
                return;

            var configurations = new List<SystemConfiguration>
            {
                new SystemConfiguration
                {
                    ConfigurationKey = "DefaultSamplingInterval",
                    ConfigurationValue = _configService.GetStringSetting("DefaultSamplingInterval", "1.0"),
                    Description = "默认采样间隔（秒）",
                    Category = "System",
                    DataType = "Decimal"
                },
                new SystemConfiguration
                {
                    ConfigurationKey = "MaxVoltage",
                    ConfigurationValue = _configService.GetStringSetting("MaxVoltage", "10.0"),
                    Description = "最大电压限制（V）",
                    Category = "Safety",
                    DataType = "Decimal"
                },
                new SystemConfiguration
                {
                    ConfigurationKey = "MaxCurrent",
                    ConfigurationValue = _configService.GetStringSetting("MaxCurrent", "170.0"),
                    Description = "最大电流限制（A）",
                    Category = "Safety",
                    DataType = "Decimal"
                },
                new SystemConfiguration
                {
                    ConfigurationKey = "MaxTemperature",
                    ConfigurationValue = _configService.GetStringSetting("MaxTemperature", "95.0"),
                    Description = "最大温度限制（°C）",
                    Category = "Safety",
                    DataType = "Decimal"
                },
                new SystemConfiguration
                {
                    ConfigurationKey = "EmergencyStopTimeout",
                    ConfigurationValue = _configService.GetStringSetting("EmergencyStopTimeout", "500"),
                    Description = "紧急停止超时（毫秒）",
                    Category = "Safety",
                    DataType = "Integer"
                },
                new SystemConfiguration
                {
                    ConfigurationKey = "DeviceCommunicationTimeout",
                    ConfigurationValue = _configService.GetStringSetting("DeviceCommunicationTimeout", "5"),
                    Description = "设备通讯超时（秒）",
                    Category = "Device",
                    DataType = "Integer"
                },
                new SystemConfiguration
                {
                    ConfigurationKey = "DataRetentionDays",
                    ConfigurationValue = _configService.GetStringSetting("DataRetentionDays", "365"),
                    Description = "数据保留天数",
                    Category = "Database",
                    DataType = "Integer"
                }
            };

            await _context.SystemConfigurations.AddRangeAsync(configurations);
            await _context.SaveChangesAsync();

            App.AlarmService.Info("数据库初始化", $"初始化了 {configurations.Count} 个系统配置");
        }

        /// <summary>
        /// 初始化实验模板数据
        /// </summary>
        private async Task SeedExperimentTemplatesAsync()
        {
            if (await _context.ExperimentTemplates.AnyAsync())
                return;

            var templates = new List<ExperimentTemplate>
            {
                new ExperimentTemplate
                {
                    Name = "恒流模式默认模板",
                    Description = "恒定电流模式的默认参数配置",
                    ExperimentType = ExperimentType.ConstantCurrent,
                    Configuration = JsonConvert.SerializeObject(new
                    {
                        Common = new
                        {
                            TargetTemperature = 60.0,
                            FlowRate1 = 10.0,
                            FlowRate2 = 15.0,
                            RepeatCount = 1
                        },
                        ConstantCurrent = new
                        {
                            TargetCurrent = 50.0,
                            Duration = 3600,
                            VoltageUpperLimit = 8.0,
                            VoltageLowerLimit = 0.5,
                            SamplingInterval = _configService.GetDoubleSetting("DefaultSamplingInterval", 1.0)
                        }
                    }),
                    IsDefault = true,
                    IsSystem = true
                },
                new ExperimentTemplate
                {
                    Name = "恒压模式默认模板",
                    Description = "恒定电压模式的默认参数配置",
                    ExperimentType = ExperimentType.ConstantVoltage,
                    Configuration = JsonConvert.SerializeObject(new
                    {
                        Common = new
                        {
                            TargetTemperature = 60.0,
                            FlowRate1 = 10.0,
                            FlowRate2 = 15.0,
                            RepeatCount = 1
                        },
                        ConstantVoltage = new
                        {
                            TargetVoltage = 2.0,
                            Duration = 3600,
                            CurrentUpperLimit = 150.0,
                            CurrentLowerLimit = 1.0,
                            SamplingInterval = _configService.GetDoubleSetting("DefaultSamplingInterval", 1.0)
                        }
                    }),
                    IsDefault = true,
                    IsSystem = true
                },
                new ExperimentTemplate
                {
                    Name = "线性提升默认模板",
                    Description = "线性提升电压模式的默认参数配置",
                    ExperimentType = ExperimentType.LinearVoltageRamp,
                    Configuration = JsonConvert.SerializeObject(new
                    {
                        Common = new
                        {
                            TargetTemperature = 60.0,
                            FlowRate1 = 10.0,
                            FlowRate2 = 15.0,
                            RepeatCount = 1
                        },
                        LinearVoltageRamp = new
                        {
                            StartVoltage = 0.5,
                            EndVoltage = 3.0,
                            RampTime = 1800,
                            UseRampTime = true,
                            CurrentUpperLimit = 150.0,
                            CurrentLowerLimit = 1.0,
                            SamplingInterval = _configService.GetDoubleSetting("DefaultSamplingInterval", 1.0),
                            HoldTimeAtEnd = 600
                        }
                    }),
                    IsDefault = true,
                    IsSystem = true
                }
            };

            await _context.ExperimentTemplates.AddRangeAsync(templates);
            await _context.SaveChangesAsync();

            App.AlarmService.Info("数据库初始化", $"初始化了 {templates.Count} 个实验模板");
        }
    }
}